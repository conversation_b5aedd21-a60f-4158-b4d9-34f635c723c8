# -*-coding:utf-8 -*-
# Author     ：wang<PERSON><PERSON>
# Email      ：<EMAIL>
# Time       ：2025/5/12 15:24
import re

from app.basic.util import html_util


class PreHtml:
    """
    使用规则或大模型预处理 html
    """

    # 先散着写，等行程规模了拆分函数
    def __init__(self, html_data: str, subject: str, task_id: str, is_wps: bool):
        self.html_data = html_data
        self.subject = subject
        self.task_id = task_id
        self.is_wps = is_wps
        self.html_list = html_util.split_html_v2(self.html_data)

    def main(self):
        for index, html_str in enumerate(self.html_list):
            if '如图所示,电源电压保持不变' in html_str:
                print()

            if '课件待替换标题' in html_str:
                html_str = ''
            pattern = re.compile(r'(<p[^<>]*?>)(\d+\.)(\s*\(\d\d\d\d.*?\))')
            if pattern.search(html_str):
                html_str = pattern.sub(
                    lambda x: f'{x.group(1)}<span data-label="quest_num" data-level="1">{x.group(2)}</span>{x.group(3)}',
                    html_str)
            self.html_list[index] = html_str

        self.html_list = [h for h in self.html_list if h]
        self.html_data = html_util.join_html(self.html_list)
        self.html_data = self.pre_serial_number()

        # wps 清洗掉标题
        if self.is_wps:
            self.html_data = re.sub(
                r'<p[^>]*?data-label="header" data-level="\d+"[^>]*?>',
                lambda x: '<p>', self.html_data)

        # 把所有的 【答案】加 discard
        self.html_data = self.html_data.replace('【答案】', '<span data-label="discard">【答案】</span>')

        # 处理 html_data，将其转换为特定格式的数组
        simplified_html_list = []
        html_elements = html_util.split_html_v2(self.html_data)

        for index, element in enumerate(html_elements):
            # 只提取 p 标签上的 data-label 属性
            data_label = ""
            if element.strip().startswith('<p'):
                # 提取 p 标签上的 data-label 属性
                p_tag_match = re.search(r'<p[^>]*data-label="([^"]*)"[^>]*>', element)
                data_label = p_tag_match.group(1) if p_tag_match else ""

            # 去掉所有 HTML 标签，保留内容
            content = html_util.del_html_tag(element).strip()

            # 组合成指定格式：index:data-label@content
            simplified_item = f"{index}:{data_label}@{content}"
            simplified_html_list.append(simplified_item)

        # 处理简化后的数据列表
        processed_result = self.process_simplified_list(simplified_html_list)

        # 返回处理后的结果和原始html_data
        return  self.html_data

    def pre_serial_number(self):
        block_list = self.get_block_list()
        for item in block_list:
            sub_html_list, min_line, max_line = item['sub_html_list'], item['min_line'], item['max_line']
            # 把一级题号标签去掉、如果没有解析标签，加上
            for _i, _s in enumerate(sub_html_list):
                _s = re.sub(
                    r'<span[^>]*?data-label="quest_num" data-level="\d"[^>]*?>(.*?)</span>',
                    lambda x: x.group(1), _s)
                if 'data-label="explanation"' not in _s:
                    _s = _s.replace('<p', '<p data-label="explanation"')
                sub_html_list[_i] = _s

            sub_html_data = html_util.join_html(sub_html_list, is_del_line=False)
            # 把所有的数据写到开始的 line，其余 line 置空
            for i in range(min_line, max_line + 1):
                if i == min_line:
                    self.html_list[i] = sub_html_data
                else:
                    self.html_list[i] = ''
        self.html_list = [h for h in self.html_list if h]
        self.html_data = html_util.join_html(self.html_list, is_del_line=False)

        # 如果是 wps 且没有一级题号，则补充假题号
        if self.is_wps and not re.search(r'<span[^>]*?data-label="quest_num" data-level="1"[^>]*?>', self.html_data):
            self.html_data = re.sub(
                r'<p[^>]*?>', lambda x: x.group() + '<span data-label="quest_num" data-level="-1">1.</span>',
                self.html_data, count=1)
        return self.html_data

    def get_block_list(self):
        """
        切割 block
        """
        def _helper():
            if analysis_list:
                min_line = min(line_list)
                max_line = max(line_list)
                group_list.append({
                    'sub_html_list': analysis_list,
                    'min_line': min_line,
                    'max_line': max_line,
                })

        group_list = []
        analysis_list = []
        line_list = []
        next_index = -1
        for index, s in enumerate(self.html_list):
            if '从图上看，起飞后第25秒时' in s:
                print()

            if next_index > index:
                continue

            if index == len(self.html_list) - 1:
                analysis_list.append(s)
                line_list.append(index)
                _helper()
                analysis_list, line_list = [], []
            elif 'data-label="explanation"' in s:
                analysis_list.append(s)
                line_list.append(index)
            elif 'data-label="header"' in s:
                _helper()
                analysis_list, line_list = [], []
            else:
                if not analysis_list:
                    continue
                # 判断 block 的结束在哪里
                # 如果 s 的下一个还是解析或者是题号，则 s 加入到当前 block
                next_s = self.html_list[index + 1]
                if 'data-label="explanation"' in next_s or ('data-label="quest_num"' in next_s and 'data-level="1"' in next_s):
                    analysis_list.append(s)
                    line_list.append(index)
                else:
                    # 往下探查 4 行，如果遇到一级题号、标题，则把其前面都加入到当前 block；如果没有遇到，则 block 截止到当前行
                    is_merge_down = False
                    # 从当前行的下一行开始下探，把当前行先加到 temp list
                    temp_list = [self.html_list[index]]
                    temp_line_list = [index]
                    for _i in range(index + 1, index + 5):
                        tmp = self.html_list[_i]
                        if ('data-label="quest_num"' in tmp and 'data-level="1"' in tmp) or 'data-label="header"' in tmp:
                            is_merge_down = True
                            next_index = _i
                            break
                        elif _i == len(self.html_list) - 1:
                            temp_list.append(tmp)
                            temp_line_list.append(_i)
                            is_merge_down = True
                            next_index = _i
                            break
                        else:
                            temp_list.append(tmp)
                            temp_line_list.append(_i)
                    if is_merge_down:
                        analysis_list += temp_list
                        line_list += temp_line_list

                    _helper()
                    analysis_list, line_list = [], []

        return group_list

    def process_simplified_list(self, simplified_html_list):
        """
        处理简化后的HTML列表，按照要求进行分组和拆分

        Args:
            simplified_html_list: 格式为 "index:data-label@content" 的列表

        Returns:
            list: 处理后的结果，格式为 [{index: 0, list: [[...], [...]]}, ...]
        """
        # 1. 按照 header 进行分组
        groups = self._group_by_header(simplified_html_list)

        # 2. 处理每个组，找到 explanation 后面的无 label 内容
        result = []
        for group_index, group in enumerate(groups):
            explanation_blocks = self._find_explanation_blocks(group)
            if explanation_blocks:
                # 3. 对每个 explanation 块进行拆分（最多5行无label内容）
                split_blocks = self._split_explanation_blocks(explanation_blocks)
                if split_blocks:
                    result.append({
                        'index': group_index,
                        'list': split_blocks
                    })

        return result

    def _group_by_header(self, simplified_html_list):
        """
        按照 header 标签进行分组

        Args:
            simplified_html_list: 简化的HTML列表

        Returns:
            list: 分组后的二维数组
        """
        groups = []
        current_group = []

        for item in simplified_html_list:
            # 解析格式：index:data-label@content
            parts = item.split(':', 1)
            if len(parts) < 2:
                continue

            label_content = parts[1]
            label_parts = label_content.split('@', 1)
            if len(label_parts) < 2:
                continue

            data_label = label_parts[0]

            # 如果遇到 header，开始新的分组
            if data_label == 'header':
                if current_group:
                    groups.append(current_group)
                current_group = [item]
            else:
                current_group.append(item)

        # 添加最后一个分组
        if current_group:
            groups.append(current_group)

        return groups

    def _find_explanation_blocks(self, group):
        """
        在组中找到 explanation 后面的连续无 label 内容

        Args:
            group: 一个分组的数据

        Returns:
            list: explanation 块的列表，每个块包含前3行上文和后续无label内容
        """
        explanation_blocks = []
        i = 0

        while i < len(group):
            item = group[i]

            # 解析当前项
            parts = item.split(':', 1)
            if len(parts) < 2:
                i += 1
                continue

            label_content = parts[1]
            label_parts = label_content.split('@', 1)
            if len(label_parts) < 2:
                i += 1
                continue

            data_label = label_parts[0]

            # 如果是 explanation
            if data_label == 'explanation':
                # 收集当前 explanation
                explanation_item = item
                j = i + 1

                # 连续收集紧跟在explanation后面的无 label 内容
                unlabeled_items = []
                while j < len(group):
                    next_item = group[j]
                    next_parts = next_item.split(':', 1)
                    if len(next_parts) < 2:
                        j += 1
                        continue

                    next_label_content = next_parts[1]
                    next_label_parts = next_label_content.split('@', 1)
                    if len(next_label_parts) < 2:
                        j += 1
                        continue

                    next_data_label = next_label_parts[0]

                    # 如果遇到有 label 的内容（非空label），立即停止收集
                    if next_data_label and next_data_label.strip():
                        break

                    # 如果是无 label 的内容，继续收集
                    unlabeled_items.append(next_item)
                    j += 1

                # 如果有无 label 的内容，则创建块
                if unlabeled_items:
                    # 收集前3行作为上文（如果存在）
                    context_start = max(0, i - 3)
                    context_items = group[context_start:i]

                    block = {
                        'context': context_items,
                        'explanation': explanation_item,
                        'unlabeled': unlabeled_items
                    }
                    explanation_blocks.append(block)

                # 继续从当前位置寻找下一个explanation
                i += 1
            else:
                i += 1

        return explanation_blocks

    def _split_explanation_blocks(self, explanation_blocks):
        """
        将 explanation 块拆分为更小的子块（最多5行无label内容）

        Args:
            explanation_blocks: explanation 块列表

        Returns:
            list: 拆分后的子块列表
        """
        split_blocks = []

        for block in explanation_blocks:
            context_items = block['context']
            explanation_item = block['explanation']
            unlabeled_items = block['unlabeled']

            # 将无 label 内容按每5行拆分
            for i in range(0, len(unlabeled_items), 5):
                chunk_unlabeled = unlabeled_items[i:i+5]

                # 组合：前3行上文 + explanation + 当前chunk的无label内容
                sub_block = context_items + [explanation_item] + chunk_unlabeled
                split_blocks.append(sub_block)

        return split_blocks


if __name__ == '__main__':
    url = 'https://hexin-worksheet.oss-cn-shanghai.aliyuncs.com/static/ai-util/test_html_fix.html'
    import requests
    html_data = requests.get(url).content.decode('utf-8')
    PreHtml(html_data, 'biology', '111111').main()
