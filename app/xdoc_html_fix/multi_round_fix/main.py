# -*-coding:utf-8 -*-
# Author     ：wang<PERSON><PERSON>
# Email      ：<EMAIL>
# Time       ：2025/5/12 15:26
import re
import time

from app.enums.subject import Subject
from app.xdoc_html_fix.multi_round_fix.pre_html import PreHtml
from app.xdoc_html_fix.multi_round_fix.get_error_block import GetErrorBlock
from app.xdoc_html_fix.multi_round_fix.fix_error_block_v2 import FixErrorBlockV2
from app.xdoc_html_fix.clean import material_fix
from app.xdoc_html_fix.multi_round_fix.post_html import PostHtml
from app.basic.util import html_util, utils
from app.basic.log import logger
from app.basic.baseerror import VerifyError
from app.basic.util import db_util


class MultiRoundFix:

    def __init__(
            self,
            html_data: str,
            subject: str,
            task_id: str,
            html_url: str,
            tag: str,
            task_type: str,
            is_ai_edit: bool,
            is_wps: bool,
    ):
        self.html_data = html_data
        self.subject = subject
        self.task_id = task_id
        self.html_url = html_url
        self.tag = tag
        self.task_type = task_type
        self.is_ai_edit = is_ai_edit
        self.is_wps = is_wps

        self.html_list = []
        self.success_sn_list = []

        # 统计
        self.stat = {
            'cost_token_1': 0,
            'cost_token_2': 0,
            'error_block_count_1': 0,
            'error_block_count_2': 0,
            'fixed_block_count_1': 0,
            'fixed_block_count_2': 0,
            'cost_time': 0,
        }

    async def main(self):
        try:
            logger.info(f"{self.task_id} MultiRoundFix start.")
            start_time = time.time()
            # 预处理 HTML
            self.html_data = PreHtml(self.html_data, self.subject, self.task_id, self.is_wps).main()

            origin_count, count = 2, 2
            round_index = 1
            # 创建 html 修复任务表
            parent_id = db_util.html_fix_task_create(self.task_id, self.html_url, self.subject, origin_count, self.tag)

            # 打材料题开始和结束标记
            if self.is_run_material_fix():
                logger.info(f'{self.task_id} Start material fix.')
                scene = 'wps_plugin' if self.is_wps else ''
                self.html_data, cost_time, cost_token = await material_fix.material_fix(
                    self.html_data, subject=self.subject, scene=scene)

            while count:
                logger.info(f'{self.task_id} start round {round_index}')
                # 拆 html 加 lien 标记
                self.html_list = html_util.split_html_v2(self.html_data, is_add_line=True)

                # 获取到需要处理的 block
                error_block_list, sn_list = GetErrorBlock(
                    self.html_list, round_index, self.success_sn_list).main()
                logger.info(f'{self.task_id} len error_block_list={len(error_block_list)} sn_list={sn_list}')
                self.stat[f'error_block_count_{round_index}'] += len(error_block_list)

                # 处理 error block
                self.html_data, self.stat = await FixErrorBlockV2(
                    self.html_list, error_block_list, self.task_id, self.subject, self.stat,
                    round_index, self.success_sn_list, parent_id, self.tag
                ).main()
                count -= 1
                round_index += 1

            # 后处理 html
            self.html_data = PostHtml(self.html_data).main()

            cost_time = int(time.time() - start_time)
            self.stat.update({'cost_time': cost_time})
            db_util.html_fix_task_update(parent_id, self.stat)
            logger.info(f"{self.task_id} stat={self.stat}")
            logger.info(f"{self.task_id} MultiRoundFix success.")
            return self.html_data, self.stat
        except Exception as e:
            error_info = f"{self.task_id} MultiRoundFix error. error_info={utils.format_error()}"
            logger.info(error_info)
            raise VerifyError(error_info)

    def is_run_material_fix(self):
        """
        判断要不要跑 material fix
        """
        if self.is_ai_edit:
            if self.subject in (
            Subject.geography.name, Subject.biology.name, Subject.english.name, Subject.chinese.name,
            Subject.politics.name, Subject.history.name):
                return True
        else:
            if self.subject in (
                    Subject.geography.name, Subject.politics.name, Subject.history.name, Subject.english.name,
                    Subject.chinese.name):
                return True

        return False
